import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'

function App() {
  const [count, setCount] = useState(0)
  const [bgColor, setBgColor] = useState('bg-gradient-to-br from-purple-400 to-pink-400')

  const colors = [
    'bg-gradient-to-br from-purple-400 to-pink-400',
    'bg-gradient-to-br from-blue-400 to-cyan-400',
    'bg-gradient-to-br from-green-400 to-blue-500',
    'bg-gradient-to-br from-yellow-400 to-orange-500',
    'bg-gradient-to-br from-red-400 to-pink-500',
    'bg-gradient-to-br from-indigo-400 to-purple-500'
  ]

  const changeBg = () => {
    const randomColor = colors[Math.floor(Math.random() * colors.length)]
    setBgColor(randomColor)
  }

  return (
    <div className={`min-h-screen ${bgColor} flex flex-col items-center justify-center p-8 transition-all duration-500`}>
      <div className="bg-white bg-opacity-20 backdrop-blur-lg rounded-3xl p-8 shadow-2xl border border-white border-opacity-30">
        <div className="flex justify-center space-x-8 mb-8">
          <a href="https://vite.dev" target="_blank" className="hover:scale-110 transition-transform duration-300">
            <img src={viteLogo} className="h-16 w-16 animate-spin" alt="Vite logo" />
          </a>
          <a href="https://react.dev" target="_blank" className="hover:scale-110 transition-transform duration-300">
            <img src={reactLogo} className="h-16 w-16 animate-pulse" alt="React logo" />
          </a>
        </div>

        <h1 className="text-4xl font-bold text-white text-center mb-8">
          🎉 Fun App 🎉
        </h1>

        <div className="text-center space-y-6">
          <div className="bg-white bg-opacity-30 rounded-2xl p-6">
            <button
              onClick={() => setCount((count) => count + 1)}
              className="bg-white bg-opacity-90 hover:bg-white text-gray-800 font-bold py-3 px-6 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              🚀 Count is {count} 🚀
            </button>
          </div>

          <div className="bg-white bg-opacity-30 rounded-2xl p-6">
            <button
              onClick={changeBg}
              className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-purple-600 hover:to-pink-500 text-white font-bold py-3 px-6 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              🌈 Change Background 🌈
            </button>
          </div>

          <p className="text-white text-opacity-90 text-lg font-medium">
            Built with ⚡ Vite + ⚛️ React + 🎨 Tailwind CSS
          </p>
        </div>
      </div>
    </div>
  )
}

export default App
